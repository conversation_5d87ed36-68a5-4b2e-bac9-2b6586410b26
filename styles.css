/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
  position: relative;
}

/* Animated background particles */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

/* Main container */
.container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    background-position: 200% 0;
  }
  50% {
    background-position: -200% 0;
  }
}

/* Header */
.game-header {
  margin-bottom: 2rem;
}

.game-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.game-title i {
  color: #667eea;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.game-subtitle {
  color: #666;
  font-size: 1.1rem;
  font-weight: 300;
}

/* Score board */
.score-board {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  padding: 1rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.score-item i {
  font-size: 1.2rem;
  margin-bottom: 0.2rem;
}

.player-score i {
  color: #e74c3c;
}
.tie-score i {
  color: #f39c12;
}
.ai-score i {
  color: #3498db;
}

.score-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 400;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

/* Game status */
.game-status {
  margin-bottom: 2rem;
}

.current-player {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.player-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
  transition: all 0.3s ease;
}

.player-indicator.ai {
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

/* Game board */
.game-board-container {
  position: relative;
  margin-bottom: 2rem;
  display: inline-block;
}

.game-board {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  background: #34495e;
  padding: 8px;
  border-radius: 15px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.cell {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cell::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.cell:hover::before {
  left: 100%;
}

.cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background: #f8f9fa;
}

.cell.x {
  color: #e74c3c;
  animation: popIn 0.3s ease;
}

.cell.o {
  color: #3498db;
  animation: popIn 0.3s ease;
}

@keyframes popIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  80% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.cell.disabled {
  pointer-events: none;
  opacity: 0.7;
}

/* Winning line animation */
.winning-line {
  position: absolute;
  background: linear-gradient(90deg, #f39c12, #e67e22);
  height: 4px;
  border-radius: 2px;
  opacity: 0;
  transform-origin: left center;
  transition: all 0.5s ease;
  box-shadow: 0 0 10px rgba(243, 156, 18, 0.6);
}

.winning-line.show {
  opacity: 1;
  animation: drawLine 0.5s ease;
}

@keyframes drawLine {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

/* Controls */
.game-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.difficulty-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #333;
}

.difficulty-selector select {
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.difficulty-selector select:focus {
  outline: none;
  border-color: #667eea;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
  box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(149, 165, 166, 0.4);
}

/* Game message modal */
.game-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.game-message.show {
  opacity: 1;
  visibility: visible;
}

.message-content {
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  transition: transform 0.3s ease;
  max-width: 400px;
  width: 90%;
}

.game-message.show .message-content {
  transform: scale(1);
}

.message-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.message-icon.win {
  color: #27ae60;
}
.message-icon.lose {
  color: #e74c3c;
}
.message-icon.tie {
  color: #f39c12;
}

.message-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
}

/* Additional animations */
.cell.winning {
  animation: celebrate 0.6s ease-in-out;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white !important;
}

@keyframes celebrate {
  0%,
  100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1) rotate(5deg);
  }
  50% {
    transform: scale(1.2) rotate(-5deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
}

.score-value.updated {
  animation: scoreUpdate 0.5s ease;
}

@keyframes scoreUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
    color: #27ae60;
  }
  100% {
    transform: scale(1);
  }
}

.game-board.thinking {
  animation: thinking 1s ease-in-out infinite;
}

@keyframes thinking {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.btn.clicked {
  animation: buttonClick 0.2s ease;
}

@keyframes buttonClick {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Confetti animation for wins */
.confetti {
  position: fixed;
  top: -10px;
  left: 50%;
  width: 10px;
  height: 10px;
  background: #f39c12;
  animation: confetti-fall 3s linear infinite;
  z-index: 1001;
}

.confetti:nth-child(odd) {
  background: #e74c3c;
}
.confetti:nth-child(3n) {
  background: #3498db;
}
.confetti:nth-child(4n) {
  background: #27ae60;
}
.confetti:nth-child(5n) {
  background: #9b59b6;
}

@keyframes confetti-fall {
  0% {
    transform: translateX(-50%) translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Loading spinner for AI thinking */
.ai-thinking {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .container {
    padding: 1.5rem;
    margin: 1rem;
  }

  .game-title {
    font-size: 2rem;
  }

  .cell {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }

  .control-buttons {
    flex-direction: column;
  }

  .score-board {
    flex-direction: column;
    gap: 1rem;
  }

  .score-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
