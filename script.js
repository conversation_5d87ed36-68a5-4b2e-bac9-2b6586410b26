class TicTacToeGame {
  constructor() {
    this.board = Array(9).fill("");
    this.currentPlayer = "X"; // X is human, O is AI
    this.gameActive = true;
    this.scores = {
      player: 0,
      ai: 0,
      ties: 0,
    };
    this.difficulty = "medium";

    this.winningCombinations = [
      [0, 1, 2],
      [3, 4, 5],
      [6, 7, 8], // rows
      [0, 3, 6],
      [1, 4, 7],
      [2, 5, 8], // columns
      [0, 4, 8],
      [2, 4, 6], // diagonals
    ];

    this.initializeGame();
    this.createParticles();
  }

  initializeGame() {
    this.cells = document.querySelectorAll(".cell");
    this.resetBtn = document.getElementById("resetBtn");
    this.resetScoreBtn = document.getElementById("resetScoreBtn");
    this.difficultySelect = document.getElementById("difficulty");
    this.gameMessage = document.getElementById("gameMessage");
    this.playAgainBtn = document.getElementById("playAgainBtn");
    this.currentPlayerText = document.getElementById("currentPlayerText");
    this.playerIndicator = document.getElementById("playerIndicator");

    this.bindEvents();
    this.updateDisplay();
    this.loadScores();
  }

  bindEvents() {
    console.log("Binding events to", this.cells.length, "cells");
    this.cells.forEach((cell, index) => {
      console.log("Adding click listener to cell", index);
      cell.addEventListener("click", () => {
        console.log("Cell", index, "clicked!");
        this.handleCellClick(index);
      });
    });

    this.resetBtn.addEventListener("click", () => {
      this.resetBtn.classList.add("clicked");
      setTimeout(() => this.resetBtn.classList.remove("clicked"), 200);
      this.resetGame();
    });
    this.resetScoreBtn.addEventListener("click", () => {
      this.resetScoreBtn.classList.add("clicked");
      setTimeout(() => this.resetScoreBtn.classList.remove("clicked"), 200);
      this.resetScores();
    });
    this.playAgainBtn.addEventListener("click", () => this.hideMessage());
    this.difficultySelect.addEventListener("change", (e) => {
      this.difficulty = e.target.value;
      this.resetGame();
    });

    // Close message on background click
    this.gameMessage.addEventListener("click", (e) => {
      if (e.target === this.gameMessage) {
        this.hideMessage();
      }
    });
  }

  handleCellClick(index) {
    console.log("handleCellClick called with index:", index);
    console.log("gameActive:", this.gameActive);
    console.log("board[index]:", this.board[index]);
    console.log("currentPlayer:", this.currentPlayer);

    if (
      !this.gameActive ||
      this.board[index] !== "" ||
      this.currentPlayer !== "X"
    ) {
      console.log("Click rejected - conditions not met");
      return;
    }

    console.log("Making move for player X at index:", index);

    this.makeMove(index, "X");

    if (this.gameActive && this.currentPlayer === "O") {
      this.disableCells();
      this.makeAIMove();
      // enableCells will be called after AI move completes
      setTimeout(() => {
        this.enableCells();
      }, 800); // Slightly longer than AI thinking time
    }
  }

  makeMove(index, player) {
    this.board[index] = player;
    this.cells[index].textContent = player;
    this.cells[index].classList.add(player.toLowerCase());

    const result = this.checkGameEnd();
    if (result) {
      this.endGame(result);
    } else {
      this.currentPlayer = this.currentPlayer === "X" ? "O" : "X";
      this.updateDisplay();
    }
  }

  makeAIMove() {
    if (!this.gameActive) return;

    // Add thinking animation
    this.showAIThinking(true);

    // Use setTimeout to make AI move asynchronous
    setTimeout(() => {
      let move;
      switch (this.difficulty) {
        case "easy":
          move = this.getRandomMove();
          break;
        case "medium":
          move =
            Math.random() < 0.7 ? this.getBestMove() : this.getRandomMove();
          break;
        case "hard":
          move =
            Math.random() < 0.9 ? this.getBestMove() : this.getRandomMove();
          break;
        case "impossible":
          move = this.getBestMove();
          break;
        default:
          move = this.getBestMove();
      }

      // Remove thinking animation
      this.showAIThinking(false);

      if (move !== -1) {
        this.makeMove(move, "O");
      }
    }, 300); // Small delay for thinking animation
  }

  getRandomMove() {
    const availableMoves = this.board
      .map((cell, index) => (cell === "" ? index : null))
      .filter((val) => val !== null);

    return availableMoves.length > 0
      ? availableMoves[Math.floor(Math.random() * availableMoves.length)]
      : -1;
  }

  getBestMove() {
    let bestScore = -Infinity;
    let bestMove = -1;

    for (let i = 0; i < 9; i++) {
      if (this.board[i] === "") {
        this.board[i] = "O";
        let score = this.minimax(this.board, 0, false, -Infinity, Infinity);
        this.board[i] = "";

        if (score > bestScore) {
          bestScore = score;
          bestMove = i;
        }
      }
    }

    return bestMove;
  }

  minimax(board, depth, isMaximizing, alpha, beta) {
    const result = this.checkWinner(board);

    if (result === "O") return 10 - depth;
    if (result === "X") return depth - 10;
    if (result === "tie") return 0;

    if (isMaximizing) {
      let maxEval = -Infinity;
      for (let i = 0; i < 9; i++) {
        if (board[i] === "") {
          board[i] = "O";
          let eval = this.minimax(board, depth + 1, false, alpha, beta);
          board[i] = "";
          maxEval = Math.max(maxEval, eval);
          alpha = Math.max(alpha, eval);
          if (beta <= alpha) break;
        }
      }
      return maxEval;
    } else {
      let minEval = Infinity;
      for (let i = 0; i < 9; i++) {
        if (board[i] === "") {
          board[i] = "X";
          let eval = this.minimax(board, depth + 1, true, alpha, beta);
          board[i] = "";
          minEval = Math.min(minEval, eval);
          beta = Math.min(beta, eval);
          if (beta <= alpha) break;
        }
      }
      return minEval;
    }
  }

  checkGameEnd() {
    return this.checkWinner(this.board);
  }

  checkWinner(board) {
    // Check for winner
    for (let combination of this.winningCombinations) {
      const [a, b, c] = combination;
      if (board[a] && board[a] === board[b] && board[a] === board[c]) {
        return { winner: board[a], line: combination };
      }
    }

    // Check for tie
    if (board.every((cell) => cell !== "")) {
      return { winner: "tie" };
    }

    return null;
  }

  endGame(result) {
    this.gameActive = false;

    if (result.winner === "tie") {
      this.scores.ties++;
      this.showMessage("It's a Tie!", "tie", "🤝");
      this.playSound("tie");
    } else if (result.winner === "X") {
      this.scores.player++;
      this.showMessage("You Win!", "win", "🎉");
      this.drawWinningLine(result.line);
      this.highlightWinningCells(result.line);
      this.createConfetti();
      this.playSound("win");
    } else {
      this.scores.ai++;
      this.showMessage("AI Wins!", "lose", "🤖");
      this.drawWinningLine(result.line);
      this.highlightWinningCells(result.line);
      this.playSound("lose");
    }

    this.updateScoreDisplay();
    this.saveScores();
  }

  drawWinningLine(line) {
    const winningLine = document.getElementById("winningLine");
    const [start, , end] = line;

    const startCell = this.cells[start];
    const endCell = this.cells[end];
    const startRect = startCell.getBoundingClientRect();
    const endRect = endCell.getBoundingClientRect();
    const boardRect = document
      .querySelector(".game-board")
      .getBoundingClientRect();

    const startX = startRect.left + startRect.width / 2 - boardRect.left;
    const startY = startRect.top + startRect.height / 2 - boardRect.top;
    const endX = endRect.left + endRect.width / 2 - boardRect.left;
    const endY = endRect.top + endRect.height / 2 - boardRect.top;

    const length = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2);
    const angle = (Math.atan2(endY - startY, endX - startX) * 180) / Math.PI;

    winningLine.style.width = `${length}px`;
    winningLine.style.left = `${startX}px`;
    winningLine.style.top = `${startY}px`;
    winningLine.style.transform = `rotate(${angle}deg)`;
    winningLine.classList.add("show");
  }

  showMessage(text, type, icon) {
    const messageIcon = document.getElementById("messageIcon");
    const messageText = document.getElementById("messageText");

    messageIcon.textContent = icon;
    messageIcon.className = `message-icon ${type}`;
    messageText.textContent = text;

    this.gameMessage.classList.add("show");
  }

  hideMessage() {
    this.gameMessage.classList.remove("show");
    this.resetGame();
  }

  resetGame() {
    this.board = Array(9).fill("");
    this.currentPlayer = "X";
    this.gameActive = true;

    this.cells.forEach((cell) => {
      cell.textContent = "";
      cell.className = "cell";
    });

    document.getElementById("winningLine").classList.remove("show");
    this.updateDisplay();
  }

  resetScores() {
    this.scores = { player: 0, ai: 0, ties: 0 };
    this.updateScoreDisplay();
    this.saveScores();
  }

  updateDisplay() {
    if (this.currentPlayer === "X") {
      this.currentPlayerText.textContent = "Your Turn";
      this.playerIndicator.innerHTML = '<i class="fas fa-times"></i>';
      this.playerIndicator.classList.remove("ai");
    } else {
      this.currentPlayerText.textContent = "AI Turn";
      this.playerIndicator.innerHTML = '<i class="fas fa-circle"></i>';
      this.playerIndicator.classList.add("ai");
    }
  }

  updateScoreDisplay() {
    const playerScore = document.getElementById("playerScore");
    const aiScore = document.getElementById("aiScore");
    const tieScore = document.getElementById("tieScore");

    playerScore.textContent = this.scores.player;
    aiScore.textContent = this.scores.ai;
    tieScore.textContent = this.scores.ties;

    // Add animation to updated score
    playerScore.classList.add("updated");
    aiScore.classList.add("updated");
    tieScore.classList.add("updated");

    setTimeout(() => {
      playerScore.classList.remove("updated");
      aiScore.classList.remove("updated");
      tieScore.classList.remove("updated");
    }, 500);
  }

  disableCells() {
    this.cells.forEach((cell) => cell.classList.add("disabled"));
  }

  enableCells() {
    this.cells.forEach((cell) => cell.classList.remove("disabled"));
  }

  saveScores() {
    localStorage.setItem("ticTacToeScores", JSON.stringify(this.scores));
  }

  loadScores() {
    const saved = localStorage.getItem("ticTacToeScores");
    if (saved) {
      this.scores = JSON.parse(saved);
      this.updateScoreDisplay();
    }
  }

  createParticles() {
    const particlesContainer = document.getElementById("particles");
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement("div");
      particle.className = "particle";
      particle.style.left = Math.random() * 100 + "%";
      particle.style.top = Math.random() * 100 + "%";
      particle.style.animationDelay = Math.random() * 6 + "s";
      particle.style.animationDuration = Math.random() * 3 + 3 + "s";
      particlesContainer.appendChild(particle);
    }
  }

  showAIThinking(show) {
    const gameBoard = document.querySelector(".game-board");
    const currentPlayerText = this.currentPlayerText;

    if (show) {
      gameBoard.classList.add("thinking");
      currentPlayerText.innerHTML =
        'AI Thinking <div class="ai-thinking"></div>';
    } else {
      gameBoard.classList.remove("thinking");
      this.updateDisplay();
    }
  }

  highlightWinningCells(line) {
    line.forEach((index) => {
      this.cells[index].classList.add("winning");
    });
  }

  createConfetti() {
    const confettiCount = 50;
    const body = document.body;

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement("div");
      confetti.className = "confetti";
      confetti.style.left = Math.random() * 100 + "%";
      confetti.style.animationDelay = Math.random() * 3 + "s";
      confetti.style.animationDuration = Math.random() * 2 + 2 + "s";
      body.appendChild(confetti);

      // Remove confetti after animation
      setTimeout(() => {
        if (confetti.parentNode) {
          confetti.parentNode.removeChild(confetti);
        }
      }, 5000);
    }
  }

  playSound(type) {
    // Create audio context for sound effects simulation
    if (
      typeof AudioContext !== "undefined" ||
      typeof webkitAudioContext !== "undefined"
    ) {
      const AudioContextClass = AudioContext || webkitAudioContext;
      const audioContext = new AudioContextClass();

      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      let frequency, duration;

      switch (type) {
        case "win":
          // Happy ascending notes
          frequency = 523.25; // C5
          duration = 0.3;
          oscillator.frequency.setValueAtTime(
            frequency,
            audioContext.currentTime
          );
          oscillator.frequency.setValueAtTime(
            659.25,
            audioContext.currentTime + 0.1
          ); // E5
          oscillator.frequency.setValueAtTime(
            783.99,
            audioContext.currentTime + 0.2
          ); // G5
          break;
        case "lose":
          // Descending sad notes
          frequency = 523.25; // C5
          duration = 0.4;
          oscillator.frequency.setValueAtTime(
            frequency,
            audioContext.currentTime
          );
          oscillator.frequency.setValueAtTime(
            466.16,
            audioContext.currentTime + 0.1
          ); // Bb4
          oscillator.frequency.setValueAtTime(
            392.0,
            audioContext.currentTime + 0.2
          ); // G4
          oscillator.frequency.setValueAtTime(
            349.23,
            audioContext.currentTime + 0.3
          ); // F4
          break;
        case "tie":
          // Neutral tone
          frequency = 440; // A4
          duration = 0.2;
          break;
        default:
          frequency = 440;
          duration = 0.1;
      }

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + duration
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration);
    }
  }
}

// Initialize the game when the page loads
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM Content Loaded");
  try {
    const game = new TicTacToeGame();
    console.log("Game created successfully");

    // Add a simple test click handler to verify DOM is working
    document.body.addEventListener("click", (e) => {
      console.log("Click detected on:", e.target);
    });
  } catch (error) {
    console.error("Error creating game:", error);
  }
});
