<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic <PERSON>c <PERSON> - AI Challenge</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="game-header">
            <h1 class="game-title">
                <i class="fas fa-gamepad"></i>
                Tic Tac <PERSON>e
            </h1>
            <p class="game-subtitle">Challenge the AI</p>
        </header>

        <div class="game-info">
            <div class="score-board">
                <div class="score-item player-score">
                    <i class="fas fa-user"></i>
                    <span class="score-label">You</span>
                    <span class="score-value" id="playerScore">0</span>
                </div>
                <div class="score-item tie-score">
                    <i class="fas fa-handshake"></i>
                    <span class="score-label">Ties</span>
                    <span class="score-value" id="tieScore">0</span>
                </div>
                <div class="score-item ai-score">
                    <i class="fas fa-robot"></i>
                    <span class="score-label">AI</span>
                    <span class="score-value" id="aiScore">0</span>
                </div>
            </div>

            <div class="game-status">
                <div class="current-player">
                    <span id="currentPlayerText">Your Turn</span>
                    <div class="player-indicator" id="playerIndicator">
                        <i class="fas fa-times"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="game-board-container">
            <div class="game-board" id="gameBoard">
                <div class="cell" data-index="0"></div>
                <div class="cell" data-index="1"></div>
                <div class="cell" data-index="2"></div>
                <div class="cell" data-index="3"></div>
                <div class="cell" data-index="4"></div>
                <div class="cell" data-index="5"></div>
                <div class="cell" data-index="6"></div>
                <div class="cell" data-index="7"></div>
                <div class="cell" data-index="8"></div>
            </div>
            <div class="winning-line" id="winningLine"></div>
        </div>

        <div class="game-controls">
            <div class="difficulty-selector">
                <label for="difficulty">AI Difficulty:</label>
                <select id="difficulty">
                    <option value="easy">Easy</option>
                    <option value="medium" selected>Medium</option>
                    <option value="hard">Hard</option>
                    <option value="impossible">Impossible</option>
                </select>
            </div>
            
            <div class="control-buttons">
                <button class="btn btn-secondary" id="resetBtn">
                    <i class="fas fa-redo"></i>
                    New Game
                </button>
                <button class="btn btn-primary" id="resetScoreBtn">
                    <i class="fas fa-trophy"></i>
                    Reset Score
                </button>
            </div>
        </div>

        <div class="game-message" id="gameMessage">
            <div class="message-content">
                <div class="message-icon" id="messageIcon"></div>
                <div class="message-text" id="messageText"></div>
                <button class="btn btn-primary" id="playAgainBtn">Play Again</button>
            </div>
        </div>
    </div>

    <div class="particles" id="particles"></div>
    
    <script src="script.js"></script>
</body>
</html>
