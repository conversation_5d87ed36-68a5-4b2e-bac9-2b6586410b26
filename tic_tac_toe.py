#!/usr/bin/env python3
"""
Simple Tic Tac <PERSON>e Game with AI Opponent
Play against an intelligent AI using the minimax algorithm
"""

import random
import os
import sys

class TicTacToe:
    def __init__(self):
        self.board = [' ' for _ in range(9)]
        self.current_player = 'X'  # X is human, O is AI
        self.game_over = False
        self.winner = None
        
    def display_board(self):
        """Display the current game board"""
        os.system('cls' if os.name == 'nt' else 'clear')  # Clear screen
        print("\n🎮 TIC TAC TOE - Challenge the AI! 🤖")
        print("=" * 40)
        print(f"\nYou are X, AI is O")
        print(f"Current turn: {'Your turn' if self.current_player == 'X' else 'AI thinking...'}")
        print("\nBoard positions:")
        print(" 1 | 2 | 3 ")
        print("-----------")
        print(" 4 | 5 | 6 ")
        print("-----------")
        print(" 7 | 8 | 9 ")
        print("\nCurrent board:")
        print(f" {self.board[0]} | {self.board[1]} | {self.board[2]} ")
        print("-----------")
        print(f" {self.board[3]} | {self.board[4]} | {self.board[5]} ")
        print("-----------")
        print(f" {self.board[6]} | {self.board[7]} | {self.board[8]} ")
        print()
        
    def is_valid_move(self, position):
        """Check if the move is valid"""
        return 0 <= position <= 8 and self.board[position] == ' '
    
    def make_move(self, position, player):
        """Make a move on the board"""
        if self.is_valid_move(position):
            self.board[position] = player
            return True
        return False
    
    def check_winner(self):
        """Check if there's a winner"""
        # Winning combinations
        winning_combinations = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # Columns
            [0, 4, 8], [2, 4, 6]              # Diagonals
        ]
        
        for combo in winning_combinations:
            if (self.board[combo[0]] == self.board[combo[1]] == self.board[combo[2]] != ' '):
                return self.board[combo[0]]
        
        # Check for tie
        if ' ' not in self.board:
            return 'TIE'
        
        return None
    
    def get_available_moves(self):
        """Get list of available moves"""
        return [i for i in range(9) if self.board[i] == ' ']
    
    def minimax(self, board, depth, is_maximizing, alpha=-float('inf'), beta=float('inf')):
        """Minimax algorithm with alpha-beta pruning for AI"""
        # Create a temporary board state
        temp_board = self.board[:]
        self.board = board[:]
        
        result = self.check_winner()
        
        # Restore original board
        self.board = temp_board
        
        # Base cases
        if result == 'O':  # AI wins
            return 10 - depth
        elif result == 'X':  # Human wins
            return depth - 10
        elif result == 'TIE':  # Tie
            return 0
        
        if is_maximizing:  # AI's turn
            max_eval = -float('inf')
            for i in range(9):
                if board[i] == ' ':
                    board[i] = 'O'
                    eval_score = self.minimax(board, depth + 1, False, alpha, beta)
                    board[i] = ' '
                    max_eval = max(max_eval, eval_score)
                    alpha = max(alpha, eval_score)
                    if beta <= alpha:
                        break
            return max_eval
        else:  # Human's turn
            min_eval = float('inf')
            for i in range(9):
                if board[i] == ' ':
                    board[i] = 'X'
                    eval_score = self.minimax(board, depth + 1, True, alpha, beta)
                    board[i] = ' '
                    min_eval = min(min_eval, eval_score)
                    beta = min(beta, eval_score)
                    if beta <= alpha:
                        break
            return min_eval
    
    def get_ai_move(self, difficulty='hard'):
        """Get AI move based on difficulty"""
        available_moves = self.get_available_moves()
        
        if difficulty == 'easy':
            # Random move
            return random.choice(available_moves)
        elif difficulty == 'medium':
            # 70% optimal, 30% random
            if random.random() < 0.7:
                return self.get_best_move()
            else:
                return random.choice(available_moves)
        else:  # hard
            # Always optimal
            return self.get_best_move()
    
    def get_best_move(self):
        """Get the best move using minimax"""
        best_score = -float('inf')
        best_move = 0
        
        for i in range(9):
            if self.board[i] == ' ':
                self.board[i] = 'O'
                score = self.minimax(self.board, 0, False)
                self.board[i] = ' '
                
                if score > best_score:
                    best_score = score
                    best_move = i
        
        return best_move
    
    def play_game(self):
        """Main game loop"""
        print("🎮 Welcome to Tic Tac Toe!")
        print("\nChoose difficulty:")
        print("1. Easy (Random AI)")
        print("2. Medium (Smart AI with mistakes)")
        print("3. Hard (Perfect AI)")
        
        while True:
            try:
                choice = input("\nEnter your choice (1-3): ").strip()
                if choice == '1':
                    difficulty = 'easy'
                    break
                elif choice == '2':
                    difficulty = 'medium'
                    break
                elif choice == '3':
                    difficulty = 'hard'
                    break
                else:
                    print("Please enter 1, 2, or 3")
            except KeyboardInterrupt:
                print("\nGoodbye!")
                return
        
        print(f"\nYou chose {difficulty.upper()} difficulty!")
        input("Press Enter to start the game...")
        
        while not self.game_over:
            self.display_board()
            
            if self.current_player == 'X':  # Human turn
                try:
                    move = input("Enter your move (1-9) or 'q' to quit: ").strip().lower()
                    
                    if move == 'q':
                        print("Thanks for playing!")
                        return
                    
                    position = int(move) - 1
                    
                    if not self.is_valid_move(position):
                        print("Invalid move! Try again.")
                        input("Press Enter to continue...")
                        continue
                    
                    self.make_move(position, 'X')
                    
                except (ValueError, IndexError):
                    print("Please enter a number between 1-9 or 'q' to quit.")
                    input("Press Enter to continue...")
                    continue
                except KeyboardInterrupt:
                    print("\nGoodbye!")
                    return
                    
            else:  # AI turn
                print("AI is thinking...")
                import time
                time.sleep(1)  # Add suspense
                
                ai_move = self.get_ai_move(difficulty)
                self.make_move(ai_move, 'O')
                print(f"AI chose position {ai_move + 1}")
                time.sleep(1)
            
            # Check for winner
            winner = self.check_winner()
            if winner:
                self.display_board()
                if winner == 'X':
                    print("🎉 Congratulations! You won! 🎉")
                elif winner == 'O':
                    print("🤖 AI wins! Better luck next time! 🤖")
                else:
                    print("🤝 It's a tie! Good game! 🤝")
                
                self.game_over = True
                
                # Ask to play again
                while True:
                    try:
                        play_again = input("\nPlay again? (y/n): ").strip().lower()
                        if play_again in ['y', 'yes']:
                            # Reset game
                            self.__init__()
                            self.play_game()
                            return
                        elif play_again in ['n', 'no']:
                            print("Thanks for playing!")
                            return
                        else:
                            print("Please enter 'y' or 'n'")
                    except KeyboardInterrupt:
                        print("\nGoodbye!")
                        return
            else:
                # Switch players
                self.current_player = 'O' if self.current_player == 'X' else 'X'

def main():
    """Main function to start the game"""
    try:
        game = TicTacToe()
        game.play_game()
    except KeyboardInterrupt:
        print("\nGoodbye!")
    except Exception as e:
        print(f"An error occurred: {e}")
        print("Please try running the game again.")

if __name__ == "__main__":
    main()
