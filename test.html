<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Clicks</title>
    <style>
        .cell {
            width: 80px;
            height: 80px;
            background: lightblue;
            border: 2px solid black;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
            text-align: center;
            line-height: 80px;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <h1>Click Test</h1>
    <div class="cell" data-index="0"></div>
    <div class="cell" data-index="1"></div>
    <div class="cell" data-index="2"></div>
    
    <script>
        console.log("Test script loaded");
        
        document.addEventListener("DOMContentLoaded", () => {
            console.log("DOM loaded");
            
            const cells = document.querySelectorAll('.cell');
            console.log("Found cells:", cells.length);
            
            cells.forEach((cell, index) => {
                console.log("Adding listener to cell", index);
                cell.addEventListener('click', () => {
                    console.log("Cell", index, "clicked!");
                    cell.textContent = 'X';
                    cell.style.background = 'lightgreen';
                });
            });
        });
    </script>
</body>
</html>
