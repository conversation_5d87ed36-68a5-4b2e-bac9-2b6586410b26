# 🎮 Tic Tac Toe - AI Challenge

An aesthetically pleasing Tic Tac Toe game with an intelligent AI opponent built with HTML, CSS, and JavaScript.

## ✨ Features

### 🎨 Beautiful Design
- Modern gradient background with animated particles
- Smooth animations and hover effects
- Responsive design that works on all devices
- Glass-morphism UI elements with backdrop blur
- Winning line animations and cell celebrations

### 🤖 Smart AI Opponent
- **4 Difficulty Levels:**
  - **Easy**: Random moves for beginners
  - **Medium**: Mix of strategic and random moves
  - **Hard**: Mostly strategic with occasional mistakes
  - **Impossible**: Perfect play using minimax algorithm with alpha-beta pruning

### 🎵 Interactive Experience
- Sound effects for wins, losses, and ties
- Confetti animation for player victories
- AI thinking animations
- Button click feedback
- Score tracking with animations

### 📊 Game Features
- Real-time score tracking (Player vs AI vs Ties)
- Persistent score storage using localStorage
- Game reset and score reset functionality
- Difficulty selection during gameplay
- Winning line highlighting

## 🚀 How to Play

1. Open `index.html` in your web browser
2. Choose your preferred AI difficulty level
3. Click on any empty cell to make your move (you are X)
4. The AI will automatically make its move (O)
5. First to get three in a row wins!
6. Use "New Game" to restart or "Reset Score" to clear statistics

## 🛠️ Technical Implementation

### AI Algorithm
The AI uses the **Minimax algorithm with Alpha-Beta pruning** for optimal decision making:
- Evaluates all possible game states
- Chooses the best move to maximize its chances of winning
- Alpha-beta pruning optimizes performance by eliminating unnecessary branches
- Different difficulty levels introduce controlled randomness

### Performance Features
- Efficient game state management
- Optimized animations using CSS transforms
- Minimal DOM manipulation for smooth performance
- Web Audio API for sound generation

### Browser Compatibility
- Modern browsers with ES6+ support
- Web Audio API for sound effects
- CSS Grid and Flexbox for layout
- CSS animations and transforms

## 📁 File Structure

```
TicTacToe/
├── index.html      # Main HTML structure
├── styles.css      # All styling and animations
├── script.js       # Game logic and AI implementation
└── README.md       # This documentation
```

## 🎯 Game Rules

1. The game is played on a 3×3 grid
2. Player is X, AI is O
3. Players take turns placing their marks
4. First to get 3 marks in a row (horizontal, vertical, or diagonal) wins
5. If all 9 squares are filled without a winner, it's a tie

## 🏆 Scoring System

- **Player Win**: +1 to your score
- **AI Win**: +1 to AI score  
- **Tie Game**: +1 to tie score
- Scores persist between browser sessions

## 🎨 Customization

The game is built with modular CSS and JavaScript, making it easy to customize:
- Colors and themes in `styles.css`
- AI difficulty parameters in `script.js`
- Sound effects and animations can be modified
- Responsive breakpoints for different screen sizes

Enjoy playing against the AI! 🎮✨
